// Generated by CoffeeScript 1.12.7
(function() {
  var utils;

  utils = require('./utils');

  exports.app = {
    chunking_test: function(req, res, _, next_filter) {
      var write;
      res.setHeader('Content-Type', 'application/javascript; charset=UTF-8');
      res.writeHead(200);
      write = (function(_this) {
        return function(payload) {
          var x;
          try {
            return res.write(payload + '\n');
          } catch (error) {
            x = error;
          }
        };
      })(this);
      utils.timeout_chain([
        [
          0, (function(_this) {
            return function() {
              return write('h');
            };
          })(this)
        ], [
          1, (function(_this) {
            return function() {
              return write(Array(2049).join(' ') + 'h');
            };
          })(this)
        ], [
          5, (function(_this) {
            return function() {
              return write('h');
            };
          })(this)
        ], [
          25, (function(_this) {
            return function() {
              return write('h');
            };
          })(this)
        ], [
          125, (function(_this) {
            return function() {
              return write('h');
            };
          })(this)
        ], [
          625, (function(_this) {
            return function() {
              return write('h');
            };
          })(this)
        ], [
          3125, (function(_this) {
            return function() {
              write('h');
              return res.end();
            };
          })(this)
        ]
      ]);
      return true;
    },
    info: function(req, res, _) {
      var info;
      info = {
        websocket: this.options.websocket,
        origins: !this.options.disable_cors ? ['*:*'] : void 0,
        cookie_needed: !!this.options.jsessionid,
        entropy: utils.random32()
      };
      if (typeof this.options.base_url === 'function') {
        info.base_url = this.options.base_url();
      } else if (this.options.base_url) {
        info.base_url = this.options.base_url;
      }
      res.setHeader('Content-Type', 'application/json; charset=UTF-8');
      res.writeHead(200);
      return res.end(JSON.stringify(info));
    },
    info_options: function(req, res) {
      res.statusCode = 204;
      res.setHeader('Access-Control-Allow-Methods', 'OPTIONS, GET');
      res.setHeader('Access-Control-Max-Age', res.cache_for);
      return '';
    }
  };

}).call(this);
