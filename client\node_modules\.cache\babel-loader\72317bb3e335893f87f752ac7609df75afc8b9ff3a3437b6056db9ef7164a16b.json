{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M21.85 9.01c-.41 0-.82.24-.95.63-.26.79-1.01 1.36-1.88 1.36H4.98c-.87 0-1.62-.57-1.88-1.36-.13-.39-.53-.62-.94-.62-.66 0-1.16.64-.95 1.26.43 1.27 1.48 2.24 2.79 2.58V20c0 1.1.9 2 2 2h4v-2.89c0-1 .68-1.92 1.66-2.08 1.26-.21 2.34.76 2.34 1.97v3h4c1.1 0 2-.9 2-2v-7.14c.46-.12 2.22-.76 2.81-2.58.2-.63-.3-1.28-.96-1.27\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 8.86V10h12V8.86c.46-.12 2.22-.76 2.81-2.58.2-.63-.3-1.27-.96-1.27-.41 0-.82.24-.95.63-.26.79-1.01 1.36-1.88 1.36H6.98c-.87 0-1.62-.57-1.88-1.36-.13-.39-.53-.62-.94-.62-.66 0-1.16.64-.95 1.26C3.64 7.55 4.69 8.53 6 8.86\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11.2 2.07 8.25 6h7.5L12.8 2.07c-.4-.54-1.2-.54-1.6 0\"\n}, \"2\")], 'TempleBuddhistRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["C:/Users/<USER>/Desktop/New Requirements/Appointment Booking System/client/node_modules/@mui/icons-material/esm/TempleBuddhistRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M21.85 9.01c-.41 0-.82.24-.95.63-.26.79-1.01 1.36-1.88 1.36H4.98c-.87 0-1.62-.57-1.88-1.36-.13-.39-.53-.62-.94-.62-.66 0-1.16.64-.95 1.26.43 1.27 1.48 2.24 2.79 2.58V20c0 1.1.9 2 2 2h4v-2.89c0-1 .68-1.92 1.66-2.08 1.26-.21 2.34.76 2.34 1.97v3h4c1.1 0 2-.9 2-2v-7.14c.46-.12 2.22-.76 2.81-2.58.2-.63-.3-1.28-.96-1.27\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M6 8.86V10h12V8.86c.46-.12 2.22-.76 2.81-2.58.2-.63-.3-1.27-.96-1.27-.41 0-.82.24-.95.63-.26.79-1.01 1.36-1.88 1.36H6.98c-.87 0-1.62-.57-1.88-1.36-.13-.39-.53-.62-.94-.62-.66 0-1.16.64-.95 1.26C3.64 7.55 4.69 8.53 6 8.86\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M11.2 2.07 8.25 6h7.5L12.8 2.07c-.4-.54-1.2-.54-1.6 0\"\n}, \"2\")], 'TempleBuddhistRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}