/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export declare class NotFoundPackageJsonError extends Error {
    constructor(rootDir: string);
}
export declare class MalformedPackageJsonError extends Error {
    constructor(packageJsonPath: string);
}
