{"version": 3, "names": ["_asyncIterator", "iterable", "method", "async", "sync", "retry", "Symbol", "asyncIterator", "iterator", "call", "AsyncFromSyncIterator", "TypeError", "s", "n", "next", "prototype", "AsyncFromSyncIteratorContinuation", "apply", "arguments", "return", "value", "ret", "undefined", "Promise", "resolve", "done", "throw", "maybeError", "thr", "reject", "r", "Object", "then"], "sources": ["../../src/helpers/asyncIterator.ts"], "sourcesContent": ["/* @minVersion 7.15.9 */\n\ntype AsyncIteratorFn<T> = AsyncIterable<T>[typeof Symbol.asyncIterator];\ntype SyncIteratorFn<T> = Iterable<T>[typeof Symbol.iterator];\n\nexport default function _asyncIterator<T>(\n  iterable: AsyncIterable<T> | Iterable<T>,\n) {\n  var method: AsyncIteratorFn<T> | SyncIteratorFn<T>,\n    async: typeof Symbol.asyncIterator | \"@@asyncIterator\" | undefined,\n    sync: typeof Symbol.iterator | \"@@iterator\" | undefined,\n    retry = 2;\n\n  if (typeof Symbol !== \"undefined\") {\n    async = Symbol.asyncIterator;\n    sync = Symbol.iterator;\n  }\n\n  while (retry--) {\n    // TypeScript doesn't have in-function narrowing, and TypeScript can't narrow\n    // AsyncIterable<T> | Iterable<T> down to AsyncIterable<T>. So let's use any here.\n    if (async && (method = (iterable as any)[async]) != null) {\n      return (method as AsyncIteratorFn<T>).call(iterable as AsyncIterable<T>);\n    }\n    // Same here, TypeScript can't narrow AsyncIterable<T> | Iterable<T> down to Iterable<T>.\n    if (sync && (method = (iterable as any)[sync]) != null) {\n      return new AsyncFromSyncIterator(\n        (method as SyncIteratorFn<T>).call(iterable as Iterable<T>),\n      );\n    }\n\n    async = \"@@asyncIterator\";\n    sync = \"@@iterator\";\n  }\n\n  throw new TypeError(\"Object is not async iterable\");\n}\n\n// AsyncFromSyncIterator is actually a class that implements AsyncIterator interface\ndeclare class AsyncFromSyncIterator<T = any, TReturn = any, TNext = undefined>\n  implements AsyncIterator<T, TReturn, TNext>\n{\n  s: Iterator<T>;\n  n: Iterator<T>[\"next\"];\n  constructor(s: Iterator<T>);\n\n  next(...args: [] | [TNext]): Promise<IteratorResult<T, TReturn>>;\n  return?(\n    value?: TReturn | PromiseLike<TReturn>,\n  ): Promise<IteratorResult<T, TReturn>>;\n  throw?(e?: any): Promise<IteratorResult<T, TReturn>>;\n}\n\n// Actual implementation of AsyncFromSyncIterator starts here\n// class only exists in ES6, so we need to use the old school way\n// This makes ESLint and TypeScript complain a lot, but it's the only way\nfunction AsyncFromSyncIterator<T, TReturn = any, TNext = undefined>(s: any) {\n  // @ts-expect-error - Intentionally overriding the constructor.\n  AsyncFromSyncIterator = function (\n    this: AsyncFromSyncIterator,\n    s: Iterator<T>,\n  ) {\n    this.s = s;\n    this.n = s.next;\n  };\n  AsyncFromSyncIterator.prototype = {\n    // Initiating the \"s\" and \"n\", use \"any\" to prevent TS from complaining\n    /* SyncIterator */ s: null as any,\n    /* SyncIterator.[[Next]] */ n: null as any,\n    next: function () {\n      return AsyncFromSyncIteratorContinuation<T, TReturn>(\n        // Use \"arguments\" here for better compatibility and smaller bundle size\n        // Itentionally casting \"arguments\" to an array for the type of func.apply\n        this.n.apply(this.s, arguments as any as [] | [undefined]),\n      );\n    },\n    return: function (value) {\n      var ret = this.s[\"return\"];\n      if (ret === undefined) {\n        return Promise.resolve<IteratorReturnResult<TReturn>>({\n          // \"TReturn | PromiseLike<TReturn>\" should have been unwrapped by Awaited<T>,\n          // but TypeScript choked, let's just casting it away\n          value: value as TReturn,\n          done: true,\n        });\n      }\n      return AsyncFromSyncIteratorContinuation<T, TReturn>(\n        ret.apply(\n          this.s,\n          // Use \"arguments\" here for better compatibility and smaller bundle size\n          // Itentionally casting \"arguments\" to an array for the type of func.apply\n          arguments as any as [] | [TReturn | PromiseLike<TReturn>],\n        ),\n      );\n    },\n    throw: function (maybeError?: any) {\n      var thr = this.s[\"return\"];\n      if (thr === undefined) {\n        // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n        return Promise.reject(maybeError);\n      }\n      return AsyncFromSyncIteratorContinuation<T, TReturn>(\n        // Use \"arguments\" here for better compatibility and smaller bundle size\n        // Itentionally casting \"arguments\" to an array for the type of func.apply\n        thr.apply(this.s, arguments as any as [any]),\n      );\n    },\n  } satisfies AsyncFromSyncIterator<T, TReturn, TNext>;\n\n  function AsyncFromSyncIteratorContinuation<T, TReturn>(r: any) {\n    // This step is _before_ calling AsyncFromSyncIteratorContinuation in the spec.\n    if (Object(r) !== r) {\n      return Promise.reject(new TypeError(r + \" is not an object.\"));\n    }\n\n    var done = r.done;\n    return Promise.resolve(r.value).then<IteratorResult<T, TReturn>>(\n      function (value) {\n        return { value: value, done: done };\n      },\n    );\n  }\n\n  return new AsyncFromSyncIterator(s);\n}\n"], "mappings": ";;;;;;AAKe,SAASA,cAAcA,CACpCC,QAAwC,EACxC;EACA,IAAIC,MAA8C;IAChDC,KAAkE;IAClEC,IAAuD;IACvDC,KAAK,GAAG,CAAC;EAEX,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjCH,KAAK,GAAGG,MAAM,CAACC,aAAa;IAC5BH,IAAI,GAAGE,MAAM,CAACE,QAAQ;EACxB;EAEA,OAAOH,KAAK,EAAE,EAAE;IAGd,IAAIF,KAAK,IAAI,CAACD,MAAM,GAAID,QAAQ,CAASE,KAAK,CAAC,KAAK,IAAI,EAAE;MACxD,OAAQD,MAAM,CAAwBO,IAAI,CAACR,QAA4B,CAAC;IAC1E;IAEA,IAAIG,IAAI,IAAI,CAACF,MAAM,GAAID,QAAQ,CAASG,IAAI,CAAC,KAAK,IAAI,EAAE;MACtD,OAAO,IAAIM,qBAAqB,CAC7BR,MAAM,CAAuBO,IAAI,CAACR,QAAuB,CAC5D,CAAC;IACH;IAEAE,KAAK,GAAG,iBAAiB;IACzBC,IAAI,GAAG,YAAY;EACrB;EAEA,MAAM,IAAIO,SAAS,CAAC,8BAA8B,CAAC;AACrD;AAoBA,SAASD,qBAAqBA,CAAsCE,CAAM,EAAE;EAE1EF,qBAAqB,GAAG,SAAAA,CAEtBE,CAAc,EACd;IACA,IAAI,CAACA,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGD,CAAC,CAACE,IAAI;EACjB,CAAC;EACDJ,qBAAqB,CAACK,SAAS,GAAG;IAEbH,CAAC,EAAE,IAAW;IACLC,CAAC,EAAE,IAAW;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MAChB,OAAOE,iCAAiC,CAGtC,IAAI,CAACH,CAAC,CAACI,KAAK,CAAC,IAAI,CAACL,CAAC,EAAEM,SAAoC,CAC3D,CAAC;IACH,CAAC;IACDC,MAAM,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACvB,IAAIC,GAAG,GAAG,IAAI,CAACT,CAAC,CAAC,QAAQ,CAAC;MAC1B,IAAIS,GAAG,KAAKC,SAAS,EAAE;QACrB,OAAOC,OAAO,CAACC,OAAO,CAAgC;UAGpDJ,KAAK,EAAEA,KAAgB;UACvBK,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA,OAAOT,iCAAiC,CACtCK,GAAG,CAACJ,KAAK,CACP,IAAI,CAACL,CAAC,EAGNM,SACF,CACF,CAAC;IACH,CAAC;IACDQ,KAAK,EAAE,SAAAA,CAAUC,UAAgB,EAAE;MACjC,IAAIC,GAAG,GAAG,IAAI,CAAChB,CAAC,CAAC,QAAQ,CAAC;MAC1B,IAAIgB,GAAG,KAAKN,SAAS,EAAE;QAErB,OAAOC,OAAO,CAACM,MAAM,CAACF,UAAU,CAAC;MACnC;MACA,OAAOX,iCAAiC,CAGtCY,GAAG,CAACX,KAAK,CAAC,IAAI,CAACL,CAAC,EAAEM,SAAyB,CAC7C,CAAC;IACH;EACF,CAAoD;EAEpD,SAASF,iCAAiCA,CAAac,CAAM,EAAE;IAE7D,IAAIC,MAAM,CAACD,CAAC,CAAC,KAAKA,CAAC,EAAE;MACnB,OAAOP,OAAO,CAACM,MAAM,CAAC,IAAIlB,SAAS,CAACmB,CAAC,GAAG,oBAAoB,CAAC,CAAC;IAChE;IAEA,IAAIL,IAAI,GAAGK,CAAC,CAACL,IAAI;IACjB,OAAOF,OAAO,CAACC,OAAO,CAACM,CAAC,CAACV,KAAK,CAAC,CAACY,IAAI,CAClC,UAAUZ,KAAK,EAAE;MACf,OAAO;QAAEA,KAAK,EAAEA,KAAK;QAAEK,IAAI,EAAEA;MAAK,CAAC;IACrC,CACF,CAAC;EACH;EAEA,OAAO,IAAIf,qBAAqB,CAACE,CAAC,CAAC;AACrC", "ignoreList": []}