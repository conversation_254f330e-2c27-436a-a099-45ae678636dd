"use strict";
/*
  Copyright 2019 Google LLC

  Use of this source code is governed by an MIT-style
  license that can be found in the LICENSE file or at
  https://opensource.org/licenses/MIT.
*/
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSourcemapAssetName = void 0;
const get_source_map_url_1 = require("workbox-build/build/lib/get-source-map-url");
const upath_1 = __importDefault(require("upath"));
/**
 * If our bundled swDest file contains a sourcemap, we would invalidate that
 * mapping if we just replaced injectionPoint with the stringified manifest.
 * Instead, we need to update the swDest contents as well as the sourcemap
 * at the same time.
 *
 * See https://github.com/GoogleChrome/workbox/issues/2235
 *
 * @param {Object} compilation The current webpack compilation.
 * @param {string} swContents The contents of the swSrc file, which may or
 * may not include a valid sourcemap comment.
 * @param {string} swDest The configured swDest value.
 * @return {string|undefined} If the swContents contains a valid sourcemap
 * comment pointing to an asset present in the compilation, this will return the
 * name of that asset. Otherwise, it will return undefined.
 *
 * @private
 */
function getSourcemapAssetName(compilation, swContents, swDest) {
    const url = (0, get_source_map_url_1.getSourceMapURL)(swContents);
    if (url) {
        // Translate the relative URL to what the presumed name for the webpack
        // asset should be.
        // This *might* not be a valid asset if the sourcemap URL that was found
        // was added by another module incidentally.
        // See https://github.com/GoogleChrome/workbox/issues/2250
        const swAssetDirname = upath_1.default.dirname(swDest);
        const sourcemapURLAssetName = upath_1.default.normalize(upath_1.default.join(swAssetDirname, url));
        // Not sure if there's a better way to check for asset existence?
        if (compilation.getAsset(sourcemapURLAssetName)) {
            return sourcemapURLAssetName;
        }
    }
    return undefined;
}
exports.getSourcemapAssetName = getSourcemapAssetName;
