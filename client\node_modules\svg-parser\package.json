{"name": "svg-parser", "version": "2.0.4", "description": "Create a JSON-friendly object from an SVG string", "main": "dist/svg-parser.umd.js", "jsnext:main": "dist/svg-parser.esm.js", "module": "dist/svg-parser.esm.js", "files": ["dist", "README.md"], "scripts": {"test": "mocha", "build": "rm -rf dist && rollup -c", "pretest": "npm run build", "prepublish": "npm run lint && npm test", "lint": "eslint src"}, "prettier": {"singleQuote": true, "useTabs": true, "printWidth": 100}, "repository": {"type": "git", "url": "git+https://github.com/<PERSON>-<PERSON>/svg-parser.git"}, "keywords": ["SVG", "parser", "JSON", "object"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/<PERSON>-<PERSON>/svg-parser/issues"}, "homepage": "https://github.com/<PERSON>-<PERSON>/svg-parser#README", "devDependencies": {"@rollup/plugin-buble": "^0.21.0", "@rollup/plugin-node-resolve": "^7.0.0", "eslint": "^3.2.2", "locate-character": "^2.0.5", "mocha": "^3.0.1", "prettier": "^1.18.2", "rollup": "^1.29.0", "source-map-support": "^0.4.2"}}